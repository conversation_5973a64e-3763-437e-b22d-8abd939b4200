'use client';

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  TrendingUp, 
  TrendingDown, 
  AlertTriangle, 
  CheckCircle, 
  Clock,
  BarChart3,
  Target,
  Zap,
  ChevronDown,
  ChevronUp
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface EnhancedMessageProps {
  content: string;
  data?: any;
  timestamp: Date;
  type: 'user' | 'assistant';
}

export function EnhancedMessage({ content, data, timestamp, type }: EnhancedMessageProps) {
  const [showDetails, setShowDetails] = useState(false);

  if (type === 'user') {
    return (
      <div className="flex justify-end mb-4">
        <div className="max-w-[80%] bg-emerald-600 text-white rounded-lg px-4 py-2">
          <p className="text-sm">{content}</p>
          <p className="text-xs text-emerald-100 mt-1">
            {timestamp.toLocaleTimeString()}
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex justify-start mb-6">
      <div className="max-w-[90%] space-y-3">
        {/* Main response */}
        <div className="bg-muted/50 rounded-lg px-4 py-3">
          <div className="prose prose-sm max-w-none">
            <div className="whitespace-pre-wrap text-foreground">{content}</div>
          </div>
          <p className="text-xs text-muted-foreground mt-2">
            {timestamp.toLocaleTimeString()}
          </p>
        </div>

        {/* Enhanced data visualization */}
        {data && (
          <div className="space-y-3">
            {/* Portfolio Summary */}
            {data.portfolio_summary && (
              <Card className="border-emerald-200">
                <CardHeader className="pb-3">
                  <CardTitle className="text-sm flex items-center gap-2">
                    <BarChart3 className="h-4 w-4 text-emerald-600" />
                    Portfolio Overview
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-foreground">
                        {data.portfolio_summary.total_msmes}
                      </div>
                      <div className="text-xs text-muted-foreground">Total MSMEs</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-foreground">
                        {data.portfolio_summary.avg_score?.toFixed(1)}
                      </div>
                      <div className="text-xs text-muted-foreground">Avg Score</div>
                    </div>
                  </div>
                  
                  {/* Risk Distribution */}
                  <div className="space-y-2">
                    <div className="text-xs font-medium text-muted-foreground">Risk Distribution</div>
                    <div className="space-y-1">
                      <div className="flex items-center justify-between">
                        <span className="text-xs">Green</span>
                        <Badge variant="outline" className="text-green-600 border-green-200">
                          {data.portfolio_summary.risk_distribution?.green || 0}
                        </Badge>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-xs">Yellow</span>
                        <Badge variant="outline" className="text-yellow-600 border-yellow-200">
                          {data.portfolio_summary.risk_distribution?.yellow || 0}
                        </Badge>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-xs">Red</span>
                        <Badge variant="outline" className="text-red-600 border-red-200">
                          {data.portfolio_summary.risk_distribution?.red || 0}
                        </Badge>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Predictive Analytics */}
            {data.forecast && (
              <Card className="border-blue-200">
                <CardHeader className="pb-3">
                  <CardTitle className="text-sm flex items-center gap-2">
                    <Target className="h-4 w-4 text-blue-600" />
                    Predictive Forecast
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Trend Direction</span>
                    <div className="flex items-center gap-1">
                      {data.forecast.trend_direction === 'improving' ? (
                        <TrendingUp className="h-4 w-4 text-green-600" />
                      ) : data.forecast.trend_direction === 'declining' ? (
                        <TrendingDown className="h-4 w-4 text-red-600" />
                      ) : (
                        <div className="h-4 w-4 rounded-full bg-yellow-400" />
                      )}
                      <span className="text-sm capitalize">{data.forecast.trend_direction}</span>
                    </div>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Model Accuracy</span>
                    <div className="flex items-center gap-2">
                      <Progress 
                        value={data.forecast.model_accuracy * 100} 
                        className="w-16 h-2"
                      />
                      <span className="text-sm">{(data.forecast.model_accuracy * 100).toFixed(0)}%</span>
                    </div>
                  </div>

                  {data.forecast.risk_factors && data.forecast.risk_factors.length > 0 && (
                    <div className="space-y-1">
                      <div className="text-xs font-medium text-muted-foreground">Risk Factors</div>
                      {data.forecast.risk_factors.slice(0, 3).map((factor: string, index: number) => (
                        <div key={index} className="flex items-center gap-2">
                          <AlertTriangle className="h-3 w-3 text-amber-500" />
                          <span className="text-xs">{factor}</span>
                        </div>
                      ))}
                    </div>
                  )}
                </CardContent>
              </Card>
            )}

            {/* Intelligent Alerts */}
            {data.intelligent_alerts && data.intelligent_alerts.length > 0 && (
              <Card className="border-orange-200">
                <CardHeader className="pb-3">
                  <CardTitle className="text-sm flex items-center gap-2">
                    <Zap className="h-4 w-4 text-orange-600" />
                    AI-Powered Alerts
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  {data.intelligent_alerts.slice(0, 3).map((alert: any, index: number) => (
                    <div key={index} className="flex items-start gap-2 p-2 rounded border">
                      <div className={cn(
                        "h-2 w-2 rounded-full mt-1.5",
                        alert.severity === 'high' ? 'bg-red-500' :
                        alert.severity === 'medium' ? 'bg-yellow-500' : 'bg-green-500'
                      )} />
                      <div className="flex-1 min-w-0">
                        <div className="text-xs font-medium truncate">{alert.title}</div>
                        <div className="text-xs text-muted-foreground">{alert.description}</div>
                        {alert.auto_actionable && (
                          <Badge variant="outline" className="text-xs mt-1">
                            Auto-actionable
                          </Badge>
                        )}
                      </div>
                    </div>
                  ))}
                  
                  {data.intelligent_alerts.length > 3 && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setShowDetails(!showDetails)}
                      className="w-full text-xs"
                    >
                      {showDetails ? (
                        <>Show Less <ChevronUp className="h-3 w-3 ml-1" /></>
                      ) : (
                        <>Show {data.intelligent_alerts.length - 3} More <ChevronDown className="h-3 w-3 ml-1" /></>
                      )}
                    </Button>
                  )}
                  
                  {showDetails && data.intelligent_alerts.slice(3).map((alert: any, index: number) => (
                    <div key={index + 3} className="flex items-start gap-2 p-2 rounded border">
                      <div className={cn(
                        "h-2 w-2 rounded-full mt-1.5",
                        alert.severity === 'high' ? 'bg-red-500' :
                        alert.severity === 'medium' ? 'bg-yellow-500' : 'bg-green-500'
                      )} />
                      <div className="flex-1 min-w-0">
                        <div className="text-xs font-medium truncate">{alert.title}</div>
                        <div className="text-xs text-muted-foreground">{alert.description}</div>
                      </div>
                    </div>
                  ))}
                </CardContent>
              </Card>
            )}

            {/* AI Recommendations */}
            {data.ai_recommendations && data.ai_recommendations.length > 0 && (
              <Card className="border-emerald-200">
                <CardHeader className="pb-3">
                  <CardTitle className="text-sm flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-emerald-600" />
                    AI Recommendations
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  {data.ai_recommendations.map((recommendation: string, index: number) => (
                    <div key={index} className="flex items-start gap-2">
                      <div className="h-1.5 w-1.5 rounded-full bg-emerald-500 mt-2" />
                      <span className="text-xs text-foreground">{recommendation}</span>
                    </div>
                  ))}
                </CardContent>
              </Card>
            )}

            {/* Compliance Status */}
            {data.compliance_health && (
              <Card className="border-purple-200">
                <CardHeader className="pb-3">
                  <CardTitle className="text-sm flex items-center gap-2">
                    <Clock className="h-4 w-4 text-purple-600" />
                    Compliance Health
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Overall Score</span>
                    <div className="flex items-center gap-2">
                      <Progress 
                        value={data.compliance_health.health_score} 
                        className="w-16 h-2"
                      />
                      <span className="text-sm">{data.compliance_health.health_score}%</span>
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4 text-center">
                    <div>
                      <div className="text-lg font-bold text-red-600">
                        {data.compliance_health.overdue_tasks}
                      </div>
                      <div className="text-xs text-muted-foreground">Overdue</div>
                    </div>
                    <div>
                      <div className="text-lg font-bold text-yellow-600">
                        {data.compliance_health.upcoming_tasks}
                      </div>
                      <div className="text-xs text-muted-foreground">Upcoming</div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
