'use client';

import { EnhancedMessage } from './enhanced-message';

interface Message {
  id: string;
  type: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  data?: any;
}

interface MessageListProps {
  messages: Message[];
}

export function MessageList({ messages }: MessageListProps) {
  return (
    <div className="space-y-4">
      {messages.map((message) => (
        <EnhancedMessage
          key={message.id}
          content={message.content}
          data={message.data}
          timestamp={message.timestamp}
          type={message.type}
        />
      ))}
    </div>
  );
}
