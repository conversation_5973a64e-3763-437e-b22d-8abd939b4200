'use client';

import { Card, CardContent } from '@/components/ui/card';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Bo<PERSON>, User, ExternalLink, TrendingDown, AlertTriangle, Clock } from 'lucide-react';
import { cn } from '@/lib/utils';

interface Message {
  id: string;
  type: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  data?: any;
}

interface MessageListProps {
  messages: Message[];
}

export function MessageList({ messages }: MessageListProps) {
  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const renderStructuredData = (data: any) => {
    if (!data || typeof data !== 'object') return null;

    switch (data.type) {
      case 'risk_analysis':
        return renderRiskAnalysis(data);
      case 'score_analysis':
        return renderScoreAnalysis(data);
      case 'compliance_dashboard':
        return renderComplianceData(data);
      case 'geographic_analysis':
        return renderGeographicData(data);
      default:
        return (
          <div className="text-xs opacity-75">
            Additional data available
          </div>
        );
    }
  };

  const renderRiskAnalysis = (data: any) => (
    <div className="space-y-3">
      <div className="flex items-center gap-2 text-sm font-medium text-foreground">
        <AlertTriangle className="h-4 w-4 text-red-500" />
        Risk Analysis
        {data.ml_enhanced_count > 0 && (
          <div className="ml-2 px-2 py-1 bg-emerald-100 text-emerald-700 text-xs rounded">
            ML Enhanced
          </div>
        )}
      </div>
      <div className="grid grid-cols-2 gap-4 text-sm">
        <div className="space-y-1">
          <div className="text-muted-foreground">High Risk MSMEs</div>
          <div className="font-medium">{data.summary?.total_high_risk || 0}</div>
        </div>
        <div className="space-y-1">
          <div className="text-muted-foreground">Total Portfolio</div>
          <div className="font-medium">{data.summary?.total_msmes || 0}</div>
        </div>
      </div>

      {/* Advanced insights (invisible intelligence) */}
      {data.portfolio_health_score && (
        <div className="pt-2 border-t border-border/20">
          <div className="flex items-center justify-between text-xs">
            <span className="text-muted-foreground">Portfolio Health</span>
            <span className={cn(
              "font-medium",
              data.portfolio_health_score > 75 ? "text-green-600" :
              data.portfolio_health_score > 50 ? "text-orange-600" : "text-red-600"
            )}>
              {data.portfolio_health_score?.toFixed(0)}%
            </span>
          </div>
        </div>
      )}

      {data.msmes && data.msmes.length > 0 && (
        <Button variant="outline" size="sm" className="mt-3">
          View {data.msmes.length} MSMEs
        </Button>
      )}
    </div>
  );

  const renderScoreAnalysis = (data: any) => (
    <div className="space-y-3">
      <div className="flex items-center gap-2 text-sm font-medium text-foreground">
        <TrendingDown className="h-4 w-4 text-orange-500" />
        Score Analysis
      </div>
      <div className="grid grid-cols-2 gap-4 text-sm">
        <div className="space-y-1">
          <div className="text-muted-foreground">MSMEs Affected</div>
          <div className="font-medium">{data.msmes_affected || 0}</div>
        </div>
        <div className="space-y-1">
          <div className="text-muted-foreground">Average Drop</div>
          <div className="font-medium">{data.avg_score_drop?.toFixed(1) || 0} points</div>
        </div>
      </div>
      {data.declining_msmes && data.declining_msmes.length > 0 && (
        <Button variant="outline" size="sm" className="mt-3">
          View {data.declining_msmes.length} Declining MSMEs
        </Button>
      )}
    </div>
  );

  const renderComplianceData = (data: any) => (
    <div className="space-y-3">
      <div className="text-sm font-medium text-foreground">Compliance Status</div>
      <div className="flex gap-3">
        <div className="text-sm">
          <span className="text-red-600 font-medium">{data.overdue_count || 0}</span>
          <span className="text-muted-foreground ml-1">Overdue</span>
        </div>
        <div className="text-sm">
          <span className="text-orange-600 font-medium">{data.upcoming_count || 0}</span>
          <span className="text-muted-foreground ml-1">Upcoming</span>
        </div>
      </div>
    </div>
  );

  const renderGeographicData = (data: any) => (
    <div className="space-y-3">
      <div className="text-sm font-medium text-foreground">Geographic Analysis</div>
      {data.branches && (
        <div className="space-y-2">
          {data.branches.slice(0, 3).map((branch: any, index: number) => (
            <div key={index} className="flex items-center justify-between text-sm">
              <span className="text-foreground">{branch.name}</span>
              <span className={cn(
                "font-medium",
                branch.status === 'critical' ? 'text-red-600' :
                branch.status === 'warning' ? 'text-orange-600' : 'text-green-600'
              )}>
                {branch.high_risk_pct}% High Risk
              </span>
            </div>
          ))}
        </div>
      )}
    </div>
  );

  return (
    <div className="space-y-8">
      {messages.map((message) => (
        <div
          key={message.id}
          className={cn(
            'flex gap-4 max-w-none',
            message.type === 'user' ? 'justify-end' : 'justify-start'
          )}
        >
          {message.type === 'assistant' && (
            <div className="w-8 h-8 rounded-full bg-emerald-100 dark:bg-emerald-900 flex items-center justify-center flex-shrink-0 mt-1">
              <Bot className="h-4 w-4 text-emerald-600 dark:text-emerald-400" />
            </div>
          )}

          <div className={cn(
            'flex-1 max-w-3xl space-y-2',
            message.type === 'user' ? 'text-right' : 'text-left'
          )}>
            <div
              className={cn(
                'inline-block px-4 py-3 rounded-lg text-sm leading-relaxed',
                message.type === 'user'
                  ? 'bg-emerald-600 text-white'
                  : 'bg-muted/60 text-foreground border border-border/40'
              )}
            >
              <div className="whitespace-pre-wrap">
                {message.content}
              </div>
            </div>

            {/* Enhanced structured data rendering with AI intelligence indicators */}
            {message.data && message.type === 'assistant' && (
              <div className="mt-4 p-4 bg-muted/30 rounded-lg border border-border/40">
                {renderStructuredData(message.data)}

                {/* Enhanced AI confidence and metadata display */}
                <div className="mt-3 pt-3 border-t border-border/20 space-y-2">
                  {/* Confidence indicator */}
                  {(message.data.confidence || message.data.query_context?.confidence) && (
                    <div className="flex items-center gap-2 text-xs text-muted-foreground">
                      <div className={cn(
                        "w-2 h-2 rounded-full",
                        (message.data.confidence || message.data.query_context?.confidence) > 0.8 ? "bg-emerald-500" :
                        (message.data.confidence || message.data.query_context?.confidence) > 0.6 ? "bg-orange-500" : "bg-red-500"
                      )}></div>
                      <span>
                        AI confidence: {((message.data.confidence || message.data.query_context?.confidence) * 100).toFixed(0)}%
                        {message.data.query_context?.complexity > 0.7 && " • Complex analysis"}
                        {message.data.primary_intent && ` • Intent: ${message.data.primary_intent}`}
                      </span>
                    </div>
                  )}

                  {/* Processing metadata */}
                  {message.data.processing_time_ms && (
                    <div className="flex items-center gap-2 text-xs text-muted-foreground">
                      <Clock className="h-3 w-3" />
                      <span>Processed in {message.data.processing_time_ms}ms</span>
                      {message.data.response_type === 'enhanced_ai_mock' && (
                        <span className="px-2 py-0.5 bg-emerald-100 text-emerald-700 rounded text-xs">
                          AI Enhanced
                        </span>
                      )}
                    </div>
                  )}

                  {/* Suggested actions */}
                  {message.data.suggested_actions && message.data.suggested_actions.length > 0 && (
                    <div className="mt-3">
                      <div className="text-xs font-medium text-muted-foreground mb-2">Suggested Actions:</div>
                      <div className="flex flex-wrap gap-1">
                        {message.data.suggested_actions.slice(0, 3).map((action: string, index: number) => (
                          <Button
                            key={index}
                            variant="outline"
                            size="sm"
                            className="h-6 px-2 text-xs"
                            onClick={() => {
                              // Could trigger action or copy to input
                              console.log('Action clicked:', action);
                            }}
                          >
                            {action.length > 30 ? `${action.substring(0, 30)}...` : action}
                          </Button>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}

            <div className="text-xs text-muted-foreground">
              {formatTime(message.timestamp)}
            </div>
          </div>

          {message.type === 'user' && (
            <div className="w-8 h-8 rounded-full bg-muted flex items-center justify-center flex-shrink-0 mt-1">
              <User className="h-4 w-4 text-muted-foreground" />
            </div>
          )}
        </div>
      ))}
    </div>
  );
}
